{"Keys": ["com.unity.services.core.version", "com.unity.services.core.initializer-assembly-qualified-names", "com.unity.services.analytics.version", "com.unity.services.analytics.initializer-assembly-qualified-names", "com.unity.purchasing.version", "com.unity.purchasing.initializer-assembly-qualified-names", "com.unity.services.core.all-package-names", "com.unity.services.core.environment-name"], "Values": [{"m_Value": "1.12.5", "m_IsReadOnly": true}, {"m_Value": "Unity.Services.Core.Registration.CorePackageInitializer, Unity.Services.Core.Registration, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null;Unity.Services.Core.Internal.IInitializablePackageV2, Unity.Services.Core.Internal, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_IsReadOnly": true}, {"m_Value": "5.0.0", "m_IsReadOnly": true}, {"m_Value": "Ua2CoreInitializeCallback, Unity.Services.Analytics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_IsReadOnly": true}, {"m_Value": "4.12.1-pre.4", "m_IsReadOnly": true}, {"m_Value": "UnityEngine.Purchasing.Registration.IapCoreInitializeCallback, UnityEngine.Purchasing.Stores, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_IsReadOnly": true}, {"m_Value": "com.unity.services.core;com.unity.services.analytics;com.unity.purchasing", "m_IsReadOnly": false}, {"m_Value": "production", "m_IsReadOnly": false}]}