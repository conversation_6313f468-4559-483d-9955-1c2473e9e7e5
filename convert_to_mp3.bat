@echo off
echo تبدیل فایل‌های WAV به MP3...
echo.

REM ایجاد پوشه MP3
if not exist "mp3_files" mkdir mp3_files

REM بررسی وجود ffmpeg
where ffmpeg >nul 2>nul
if %errorlevel% neq 0 (
    echo خطا: ffmpeg یافت نشد!
    echo لطفاً ffmpeg را نصب کنید یا در PATH قرار دهید.
    echo می‌توانید از https://ffmpeg.org دانلود کنید
    pause
    exit /b 1
)

echo ffmpeg یافت شد. شروع تبدیل...
echo.

REM تبدیل همه فایل‌های WAV به MP3
for %%f in ("extracted_audio_all\*.wav") do (
    echo در حال تبدیل: %%~nf.wav
    ffmpeg -i "%%f" -acodec mp3 -ab 192k "mp3_files\%%~nf.mp3" -y >nul 2>nul
    if !errorlevel! equ 0 (
        echo ✓ تبدیل شد: %%~nf.mp3
    ) else (
        echo ✗ خطا در تبدیل: %%~nf.wav
    )
)

echo.
echo تبدیل کامل شد!
echo فایل‌های MP3 در پوشه mp3_files ذخیره شدند.
pause
