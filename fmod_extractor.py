#!/usr/bin/env python3
"""
FMOD Bank File Extractor
استخراج فایل‌های صوتی از فایل‌های .bank بازی CarX Drift Racing 2
"""

import os
import struct
import sys
from pathlib import Path

def read_uint32(f):
    """خواندن یک عدد 32 بیتی"""
    return struct.unpack('<I', f.read(4))[0]

def read_uint16(f):
    """خواندن یک عدد 16 بیتی"""
    return struct.unpack('<H', f.read(2))[0]

def write_wav_header(f, data_size, sample_rate=44100, channels=2, bits_per_sample=16):
    """نوشتن header فایل WAV"""

    # محاسبه اندازه‌ها
    byte_rate = sample_rate * channels * bits_per_sample // 8
    block_align = channels * bits_per_sample // 8

    # RIFF header
    f.write(b'RIFF')
    f.write(struct.pack('<I', 36 + data_size))  # file size - 8
    f.write(b'WAVE')

    # fmt chunk
    f.write(b'fmt ')
    f.write(struct.pack('<I', 16))  # fmt chunk size
    f.write(struct.pack('<H', 1))   # audio format (PCM)
    f.write(struct.pack('<H', channels))
    f.write(struct.pack('<I', sample_rate))
    f.write(struct.pack('<I', byte_rate))
    f.write(struct.pack('<H', block_align))
    f.write(struct.pack('<H', bits_per_sample))

    # data chunk
    f.write(b'data')
    f.write(struct.pack('<I', data_size))

def extract_fmod_bank(bank_file, output_dir):
    """استخراج فایل‌های صوتی از فایل bank"""
    
    print(f"در حال پردازش: {bank_file}")
    
    # ایجاد پوشه خروجی
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    try:
        with open(bank_file, 'rb') as f:
            # خواندن header
            header = f.read(4)
            
            if header == b'RIFF':
                print("فایل RIFF تشخیص داده شد")
                
                # خواندن اندازه فایل
                file_size = read_uint32(f)
                
                # خواندن نوع فایل
                file_type = f.read(4)
                print(f"نوع فایل: {file_type}")
                
                if file_type == b'FEV ':
                    extract_fev_file(f, output_path, bank_file)
                else:
                    print(f"نوع فایل پشتیبانی نمی‌شود: {file_type}")
                    
            else:
                print(f"Header ناشناخته: {header}")
                
    except Exception as e:
        print(f"خطا در پردازش {bank_file}: {e}")

def extract_fev_file(f, output_path, bank_file):
    """استخراج فایل FEV"""

    base_name = Path(bank_file).stem
    file_counter = 0

    # جستجو برای بخش‌های مختلف
    while True:
        try:
            chunk_id = f.read(4)
            if len(chunk_id) < 4:
                break

            chunk_size = read_uint32(f)

            print(f"Chunk: {chunk_id}, Size: {chunk_size}")

            if chunk_id == b'SND ':
                # استخراج داده‌های صوتی
                audio_data = f.read(chunk_size)

                # ذخیره فایل صوتی
                output_file = output_path / f"{base_name}_{file_counter:03d}.wav"

                with open(output_file, 'wb') as out_f:
                    # نوشتن header WAV ساده
                    write_wav_header(out_f, len(audio_data))
                    out_f.write(audio_data)

                print(f"فایل صوتی ذخیره شد: {output_file}")
                file_counter += 1

            elif chunk_id == b'LIST':
                # خواندن نوع LIST
                list_type = f.read(4)
                print(f"LIST Type: {list_type}")

                # رد کردن chunk
                f.seek(chunk_size - 4, 1)
            else:
                # رد کردن chunk
                f.seek(chunk_size, 1)

        except Exception as e:
            print(f"خطا در chunk: {e}")
            break



def main():
    """تابع اصلی"""
    
    if len(sys.argv) < 2:
        print("استفاده: python fmod_extractor.py <bank_file_or_directory> [output_directory]")
        sys.exit(1)
    
    input_path = sys.argv[1]
    output_dir = sys.argv[2] if len(sys.argv) > 2 else "extracted_audio"
    
    if os.path.isfile(input_path):
        # پردازش یک فایل
        extract_fmod_bank(input_path, output_dir)
    elif os.path.isdir(input_path):
        # پردازش همه فایل‌های .bank در پوشه
        for bank_file in Path(input_path).glob("*.bank"):
            extract_fmod_bank(str(bank_file), output_dir)
    else:
        print(f"فایل یا پوشه یافت نشد: {input_path}")

if __name__ == "__main__":
    main()
