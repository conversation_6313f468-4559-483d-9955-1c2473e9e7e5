<linker>
  <assembly fullname="AdvertySDK, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Adverty.AdUnit.Renderer.MeshUnitRenderer" preserve="all" />
    <type fullname="Adverty.InPlayUnit" preserve="all" />
    <type fullname="Adverty.AdUnit.BendSettings" preserve="nothing" serialized="true" />
    <type fullname="Adverty.AdUnit.InPlayUnitConfiguration" preserve="nothing" serialized="true" />
    <type fullname="Adverty.AdUnit.SceneContext" preserve="nothing" serialized="true" />
    <type fullname="Adverty.AdUnit.UnitBehavior" preserve="nothing" serialized="true" />
    <type fullname="Adverty.AdUnit.ViewData" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Assembly-CSharp, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="AdvertyBannerWithLifetime" preserve="all" />
    <type fullname="Animations.AnimationFinishDetector" preserve="all" />
    <type fullname="CarCameraSwitchHandler" preserve="all" />
    <type fullname="CarCollideEffects" preserve="all" />
    <type fullname="CarInteriorImagePart" preserve="all" />
    <type fullname="CarInteriorRotatingPart" preserve="all" />
    <type fullname="CarInteriorTextPart" preserve="all" />
    <type fullname="CarPopExhaust" preserve="all" />
    <type fullname="CarPreview" preserve="all" />
    <type fullname="CarPreviewSaver" preserve="all" />
    <type fullname="CarResourceProvider" preserve="all" />
    <type fullname="Cinemachine.SimpleVcamCollider" preserve="all" />
    <type fullname="Cinemachine.SimpleVerticalOffset" preserve="all" />
    <type fullname="DayTimeLightToggle" preserve="all" />
    <type fullname="Emoji.View.DiceView" preserve="all" />
    <type fullname="Emoji.View.EmojiViewWithAnimator" preserve="all" />
    <type fullname="FMODCarTurboSound" preserve="all" />
    <type fullname="FMODCarx.FMODCarCollideSound" preserve="all" />
    <type fullname="FMODCarx.FMODCarEngineSound" preserve="all" />
    <type fullname="FMODCarx.FMODPopExhaustSound" preserve="all" />
    <type fullname="FMODGearShiftingSound" preserve="all" />
    <type fullname="FMODTyreSound" preserve="all" />
    <type fullname="GameCamera.CameraGyro" preserve="all" />
    <type fullname="GameCamera.CameraSwitch" preserve="all" />
    <type fullname="GameCamera.CameraZoom" preserve="all" />
    <type fullname="GameCamera.CockpitCamera" preserve="all" />
    <type fullname="GameCamera.CommonCameraConductor" preserve="all" />
    <type fullname="GameCamera.FreeDroneCamera" preserve="all" />
    <type fullname="GameCamera.FreeDroneCameraInput" preserve="all" />
    <type fullname="GameCamera.FreeDroneCameraMovement" preserve="all" />
    <type fullname="GameCamera.FreeDroneCameraTargetAnchor" preserve="all" />
    <type fullname="GameCamera.FreeLookCameraConductor" preserve="all" />
    <type fullname="GameCamera.GarageCamera" preserve="all" />
    <type fullname="GameCamera.Offsets.CameraBodyOffsetByPreviewCarBounds" preserve="all" />
    <type fullname="GameCamera.Offsets.CameraBodyOffsetByTargetCarBounds" preserve="all" />
    <type fullname="GameCamera.Offsets.CameraForwardOffsetByPreviewCarBounds" preserve="all" />
    <type fullname="GameCamera.Offsets.FreeLookCameraOffsetByTargetCarBounds" preserve="all" />
    <type fullname="GameCamera.PhotoSessionCamera" preserve="all" />
    <type fullname="GameCamera.Presets.Binding.DriftBox3DTimeLineCameraBinding" preserve="all" />
    <type fullname="GameCamera.Presets.Binding.DriftBoxRewardsTimeLineCameraBinding" preserve="all" />
    <type fullname="GameCamera.Presets.Binding.FinishAnimationTimeLineCameraBinding" preserve="all" />
    <type fullname="GameCamera.Presets.Binding.FinishTransformTimeLineCameraBinding" preserve="all" />
    <type fullname="GameCamera.Presets.Binding.TutorialIntroAnimationTimeLineCameraBinding" preserve="all" />
    <type fullname="GameCamera.Presets.CameraPresetsManager" preserve="all" />
    <type fullname="GameCamera.Presets.Customization.CarCustomizationCameraPreset" preserve="all" />
    <type fullname="GameCamera.Presets.Customization.CarCustomizationCameraPresetsContainer" preserve="all" />
    <type fullname="GameCamera.Presets.Customization.DriverCustomizationCameraPreset" preserve="all" />
    <type fullname="GameCamera.Presets.Customization.DriverCustomizationCameraPresetsContainer" preserve="all" />
    <type fullname="GameCamera.Presets.DynoStand.DynoStandCameraPreset" preserve="all" />
    <type fullname="GameCamera.Presets.DynoStand.DynoStandCameraPresetsContainer" preserve="all" />
    <type fullname="GameCamera.Presets.Garage.GarageCameraPreset" preserve="all" />
    <type fullname="GameCamera.Presets.Garage.GarageCameraPresetsContainer" preserve="all" />
    <type fullname="GameCamera.Presets.Garage.GarageTimelineCameraPreset" preserve="all" />
    <type fullname="GameCamera.Presets.Garage.GarageTimelineCameraPresetsContainer" preserve="all" />
    <type fullname="GameCamera.Presets.Race.RaceTimelineCameraPreset" preserve="all" />
    <type fullname="GameCamera.Presets.Race.RaceTimelineCameraPresetsContainer" preserve="all" />
    <type fullname="GameCamera.RearCamera" preserve="all" />
    <type fullname="GameCamera.ReplayCamera" preserve="all" />
    <type fullname="GameCamera.ReplayCamsOnTrack" preserve="all" />
    <type fullname="GameCamera.ReplayCamsOnTrackContainer" preserve="all" />
    <type fullname="GameCamera.StaticCamera" preserve="all" />
    <type fullname="GameCamera.TVCamera" preserve="all" />
    <type fullname="GameCamera.VinylCamera" preserve="all" />
    <type fullname="GarageCameraOffsets" preserve="all" />
    <type fullname="GymkhanaVisualEffects.AnimationChangingEffect" preserve="all" />
    <type fullname="GymkhanaVisualEffects.ColorChangingEffect" preserve="all" />
    <type fullname="GymkhanaVisualEffects.VisibilityChangingEffect" preserve="all" />
    <type fullname="LocalizationBinding" preserve="all" />
    <type fullname="PlayerCarControl" preserve="all" />
    <type fullname="Race.InteractablesLogic.AnimatorBasedInteractableZoneAnimator" preserve="all" />
    <type fullname="Race.InteractablesLogic.InteractableZoneSoundEffect" preserve="all" />
    <type fullname="RaceCar" preserve="all" />
    <type fullname="RacePerformanceManager" preserve="all" />
    <type fullname="Sound.AnimationSounds" preserve="all" />
    <type fullname="SpeedUnitBinding" preserve="all" />
    <type fullname="StoreIAP" preserve="all" />
    <type fullname="SubModules.GameCore.Services.RemotePushNotificationsService" preserve="all" />
    <type fullname="TiltHorizon" preserve="all" />
    <type fullname="TrackLayout.Zones.ClippingPoints.BaseClippingPoint" preserve="all" />
    <type fullname="TrackLayout.Zones.DpBoostZones.ScarecrowDpBoostZoneDecoration" preserve="all" />
    <type fullname="TrackLayout.Zones.HanamiLanternZone" preserve="all" />
    <type fullname="TrackLayout.Zones.InteractableZones.DonutGymkhanaInteractableZonePrefab" preserve="all" />
    <type fullname="TrackLayout.Zones.InteractableZones.DpBoostZoneComponent" preserve="all" />
    <type fullname="TrackLayout.Zones.InteractableZones.DriftAlongPathActivationCondition" preserve="all" />
    <type fullname="TrackLayout.Zones.InteractableZones.DriftAroundDoubleDonutZoneActivationCondition" preserve="all" />
    <type fullname="TrackLayout.Zones.InteractableZones.DriftAroundObjectZoneActivationCondition" preserve="all" />
    <type fullname="TrackLayout.Zones.InteractableZones.DriftAroundSemiDonutZoneActivationCondition" preserve="all" />
    <type fullname="TrackLayout.Zones.InteractableZones.GymkhanaInteractableZonePrefab" preserve="all" />
    <type fullname="TrackLayout.Zones.InteractableZones.ManualInteractivityToggle" preserve="all" />
    <type fullname="TrackLayout.Zones.InteractableZones.Trigger" preserve="all" />
    <type fullname="TrackLayout.Zones.InteractableZones.TriggersPathHolder" preserve="all" />
    <type fullname="TrackLayout.Zones.ParticleWithSoundZone" preserve="all" />
    <type fullname="TrackLayout.Zones.SoundZone" preserve="all" />
    <type fullname="Tracks.Configuration.ConfigurationManager" preserve="all" />
    <type fullname="Tracks.Configuration.RaceContentManager" preserve="all" />
    <type fullname="Tracks.Minimap.MinimapData" preserve="all" />
    <type fullname="Tracks.Minimap.MinimapLevel" preserve="all" />
    <type fullname="Tracks.PositionOnSpline" preserve="all" />
    <type fullname="TriggerWithEvent" preserve="all" />
    <type fullname="TurboSoundSettings" preserve="all" />
    <type fullname="UI.Regalias.FrameRegaliaView" preserve="all" />
    <type fullname="UIContexts.UIDriftBox3DContext" preserve="all" />
    <type fullname="Utils.InterpolatedSurface.AssetHolder.BilinearSurfaceHolder" preserve="all" />
    <type fullname="WheelBurnout" preserve="all" />
    <type fullname="WheelsParticlesInstantiator" preserve="all" />
    <type fullname="WheelsParticlesObstacle" preserve="all" />
    <type fullname="WheelTires.WheelTiresStockManager" preserve="all" />
    <type fullname="WheelTires.WheelTiresStock" preserve="nothing" serialized="true" />
    <type fullname="CarCollideEffects/SoundsParams" preserve="nothing" serialized="true" />
    <type fullname="CarCollideEffects/SparksParams" preserve="nothing" serialized="true" />
    <type fullname="FMODCarx.FMODCarCollideSound/SoundsParams" preserve="nothing" serialized="true" />
    <type fullname="Tracks.Minimap.MinimapTrackData" preserve="nothing" serialized="true" />
    <type fullname="Tracks.Minimap.MinimapLevelTrackData" preserve="nothing" serialized="true" />
    <type fullname="CarInteriorPart/FillingSegment" preserve="nothing" serialized="true" />
    <type fullname="Race.InteractablesLogic.ZoneSoundEffect" preserve="nothing" serialized="true" />
    <type fullname="TrackLayout.Zones.InteractableZones.DonutAnchor" preserve="nothing" serialized="true" />
    <type fullname="TrackLayout.Zones.InteractableZones.ZoneMovingData" preserve="nothing" serialized="true" />
    <type fullname="GarageCameraOffsets/CustomizationOffset" preserve="nothing" serialized="true" />
    <type fullname="Utils.InterpolatedSurface.BilinearSurface" preserve="nothing" serialized="true" />
    <type fullname="Utils.InterpolatedSurface.BilinearSurface/Coeffisients" preserve="nothing" serialized="true" />
    <type fullname="Binding.BoolProperty" preserve="nothing" serialized="true" />
    <type fullname="GameCamera.FreeDroneCameraMovement/FovSensitivity" preserve="nothing" serialized="true" />
    <type fullname="GameCamera.Orbits" preserve="nothing" serialized="true" />
    <type fullname="GameCamera.ZoomSettings" preserve="nothing" serialized="true" />
    <type fullname="Sound.MusicPlayer/Song" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="AssetCore.Decals, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="AssetCore.Decals.DecalObject" preserve="all" />
  </assembly>
  <assembly fullname="AssetCore.Foundation, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="AssetCore.Data.PlacementData" preserve="all" />
    <type fullname="AssetCore.Splines.BendMesh" preserve="all" />
    <type fullname="AssetCore.Splines.SplineAuthoring" preserve="all" />
    <type fullname="AssetCore.Splines.SplineKitAuthoring" preserve="all" />
    <type fullname="ColorComponent" preserve="all" />
    <type fullname="EmissionComponent" preserve="all" />
    <type fullname="RespawnableProp" preserve="all" />
    <type fullname="SplineCompositionAuthoring" preserve="all" />
    <type fullname="AssetCore.Data.SplineCompositionData" preserve="nothing" serialized="true" />
    <type fullname="AssetCore.Data.SplineCompositionGroupData" preserve="nothing" serialized="true" />
    <type fullname="AssetCore.Data.SplineCompositionPointData" preserve="nothing" serialized="true" />
    <type fullname="AssetCore.Data.SplineData" preserve="nothing" serialized="true" />
    <type fullname="AssetCore.Data.SplineKitData" preserve="nothing" serialized="true" />
    <type fullname="AssetCore.Data.EntityData" preserve="nothing" serialized="true" />
    <type fullname="SplineCompositionAuthoring/Group" preserve="nothing" serialized="true" />
    <type fullname="SplineCompositionAuthoring/Point" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="AssetCore.Scene, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="AssetCore.Map" preserve="all" />
    <type fullname="AssetCore.ScenePlacement" preserve="all" />
    <type fullname="PlacementBakedStorage" preserve="all" />
    <type fullname="AssetCore.Placement/PlacementGroup" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="CarSystems.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="CarModelSystem.RearLightsSpawnMarker" preserve="all" />
    <type fullname="CarSuspensionSystem.CarSuspensionConstraints" preserve="all" />
    <type fullname="CarSuspensionSystem.CarSuspensionRigging" preserve="all" />
    <type fullname="CarSuspensionSystem.CarSuspensionTarget" preserve="all" />
    <type fullname="CarSuspensionSystem.CarSuspensionConstraints/LookAtConstraint" preserve="nothing" serialized="true" />
    <type fullname="CarSuspensionSystem.CarSuspensionConstraints/PositionConstraint" preserve="nothing" serialized="true" />
    <type fullname="CarSuspensionSystem.SuspensionData" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="CarX.Extras.Surface, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="CARXSurface" preserve="all" />
    <type fullname="CARXSurfaceTemplate" preserve="all" />
    <type fullname="CARXWheelsAdditionalParticlesData" preserve="all" />
    <type fullname="CARXWheelSkidmark" preserve="all" />
    <type fullname="CARXWheelsParticlesData" preserve="all" />
    <type fullname="CARXWheelTiresSparkData" preserve="all" />
    <type fullname="CARXPhysicMaterialProperties" preserve="nothing" serialized="true" />
    <type fullname="CARXWheelsSoundEffects" preserve="nothing" serialized="true" />
    <type fullname="CARXWheelsVisualEffects" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="CarX.Plugins.Core, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="CarX.Material" preserve="all" />
    <type fullname="CARXCar" preserve="all" />
    <type fullname="CARXMaterial" preserve="all" />
    <type fullname="CARXWheelAccessor" preserve="all" />
    <type fullname="CarX.CarDesc" preserve="nothing" serialized="true" />
    <type fullname="CarX.CarDesc/Aero" preserve="nothing" serialized="true" />
    <type fullname="CarX.CarDesc/Brakes" preserve="nothing" serialized="true" />
    <type fullname="CarX.CarDesc/Engine" preserve="nothing" serialized="true" />
    <type fullname="CarX.CarDesc/GearBox" preserve="nothing" serialized="true" />
    <type fullname="CarX.CarDesc/SteerSuspension" preserve="nothing" serialized="true" />
    <type fullname="CarX.CarDesc/Suspension" preserve="nothing" serialized="true" />
    <type fullname="CarX.CarDesc/Transmission" preserve="nothing" serialized="true" />
    <type fullname="CarX.CarDesc/Tyre" preserve="nothing" serialized="true" />
    <type fullname="CarX.CarDesc/Weight" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Cinemachine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Cinemachine.CinemachineBasicMultiChannelPerlin" preserve="all" />
    <type fullname="Cinemachine.CinemachineBlenderSettings" preserve="all" />
    <type fullname="Cinemachine.CinemachineBrain" preserve="all" />
    <type fullname="Cinemachine.CinemachineComposer" preserve="all" />
    <type fullname="Cinemachine.CinemachineFollowZoom" preserve="all" />
    <type fullname="Cinemachine.CinemachineFreeLook" preserve="all" />
    <type fullname="Cinemachine.CinemachineGroupComposer" preserve="all" />
    <type fullname="Cinemachine.CinemachineHardLookAt" preserve="all" />
    <type fullname="Cinemachine.CinemachineOrbitalTransposer" preserve="all" />
    <type fullname="Cinemachine.CinemachinePipeline" preserve="all" />
    <type fullname="Cinemachine.CinemachineTargetGroup" preserve="all" />
    <type fullname="Cinemachine.CinemachineTransposer" preserve="all" />
    <type fullname="Cinemachine.CinemachineTriggerAction" preserve="all" />
    <type fullname="Cinemachine.CinemachineVirtualCamera" preserve="all" />
    <type fullname="Cinemachine.NoiseSettings" preserve="all" />
    <type fullname="CinemachineShot" preserve="all" />
    <type fullname="CinemachineTrack" preserve="all" />
    <type fullname="Cinemachine.CinemachineBlendDefinition" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineBlenderSettings/CustomBlend" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineBrain/BrainEvent" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineBrain/VcamActivatedEvent" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineTargetGroup/Target" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineTriggerAction/ActionSettings" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineTriggerAction/ActionSettings/TriggerEvent" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineVirtualCameraBase/TransitionParams" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.LensSettings" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.NoiseSettings/NoiseParams" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.NoiseSettings/TransformNoiseParams" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.AxisState" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.AxisState/Recentering" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineFreeLook/Orbit" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineOrbitalTransposer/Heading" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="CommonUtils, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Utils.TimeManagement.TimeController" preserve="all" />
  </assembly>
  <assembly fullname="Driver, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Driver.DriverIKTarget" preserve="all" />
    <type fullname="Driver.DriverLeverAnimator" preserve="all" />
    <type fullname="Driver.DriverSteeringWheel" preserve="all" />
    <type fullname="Driver.DriverTargetAnimator" preserve="all" />
    <type fullname="Driver.LeverPatternData" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="FMODUnity, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="FMODUnity.StudioListener" preserve="all" />
    <type fullname="FMOD.GUID" preserve="nothing" serialized="true" />
    <type fullname="FMODUnity.EventReference" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="LayoutData.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="LayoutData.Behaviours.CompositeTriggerZone" preserve="all" />
    <type fullname="LayoutData.ResourceData.MinimapSpriteData/MinimapLevelData" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="RenderCore.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="RenderCore.OcclusionCulling.DynamicLODGroupTarget" preserve="all" />
    <type fullname="RenderCore.OcclusionCulling.StaticLODGroupTarget" preserve="all" />
    <type fullname="RenderCore.OcclusionCulling.StaticRendererGroupTarget" preserve="all" />
    <type fullname="RenderCore.PostProcessing.ColorAdjustments" preserve="all" />
    <type fullname="RenderCore.PostProcessing.Exposure" preserve="all" />
    <type fullname="RenderCore.PostProcessing.LiftGammaGain" preserve="all" />
    <type fullname="RenderCore.PostProcessing.Tonemapping" preserve="all" />
    <type fullname="RenderCore.PostProcessing.WhiteBalance" preserve="all" />
    <type fullname="RenderCore.Runtime.SceneProfile.HDRISky" preserve="all" />
    <type fullname="RenderCore.Runtime.SceneProfile.TextureSphere" preserve="all" />
    <type fullname="RenderCore.Scalability.Bloom" preserve="all" />
    <type fullname="RenderCore.UI.CustomMask" preserve="all" />
    <type fullname="RenderCore.UI.CustomMaskable" preserve="all" />
    <type fullname="RenderCore.UI.RenderQueueEffect" preserve="all" />
    <type fullname="RenderCore.VolumeComponents.ExponentialFog" preserve="all" />
    <type fullname="RenderCore.VolumeComponents.Lighting" preserve="all" />
    <type fullname="RenderCore.VolumeMod" preserve="all" />
    <type fullname="RenderCore.ClampedFloatParameterMod" preserve="nothing" serialized="true" />
    <type fullname="RenderCore.ClampedIntParameterMod" preserve="nothing" serialized="true" />
    <type fullname="RenderCore.ColorParameterMod" preserve="nothing" serialized="true" />
    <type fullname="RenderCore.FloatParameterMod" preserve="nothing" serialized="true" />
    <type fullname="RenderCore.MinFloatParameterMod" preserve="nothing" serialized="true" />
    <type fullname="RenderCore.NoInterpFloatParameterMod" preserve="nothing" serialized="true" />
    <type fullname="RenderCore.NoInterpIntParameterMod" preserve="nothing" serialized="true" />
    <type fullname="RenderCore.Runtime.SceneProfile.TextureSphereParameter" preserve="nothing" serialized="true" />
    <type fullname="RenderCore.NoInterpClampedFloatParameterMod" preserve="nothing" serialized="true" />
    <type fullname="RenderCore.NoInterpClampedIntParameterMod" preserve="nothing" serialized="true" />
    <type fullname="RenderCore.Scalability.BloomModeParameter" preserve="nothing" serialized="true" />
    <type fullname="RenderCore.UI.CustomMask/ClipShape" preserve="nothing" serialized="true" />
    <type fullname="RenderCore.Vector4ParameterMod" preserve="nothing" serialized="true" />
    <type fullname="RenderCore.PostProcessing.AdaptationModeParameter" preserve="nothing" serialized="true" />
    <type fullname="RenderCore.PostProcessing.ExposureModeParameter" preserve="nothing" serialized="true" />
    <type fullname="RenderCore.PostProcessing.LuminanceSourceParameter" preserve="nothing" serialized="true" />
    <type fullname="RenderCore.PostProcessing.MeteringModeParameter" preserve="nothing" serialized="true" />
    <type fullname="RenderCore.PostProcessing.TonemappingModeParameter" preserve="nothing" serialized="true" />
    <type fullname="RenderCore.UI.RenderQueueEffect/MaterialParameters" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.Addressables, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.AddressableAssets.Addressables" preserve="all" />
  </assembly>
  <assembly fullname="Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Rendering.VolumeProfile" preserve="all" />
  </assembly>
  <assembly fullname="Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.AssetBundleProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.AtlasSpriteProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.InstanceProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.LegacyResourcesProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.SceneProvider" preserve="all" />
  </assembly>
  <assembly fullname="Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="TMPro.TMP_FontAsset" preserve="all" />
    <type fullname="TMPro.FaceInfo_Legacy" preserve="nothing" serialized="true" />
    <type fullname="TMPro.FontAssetCreationSettings" preserve="nothing" serialized="true" />
    <type fullname="TMPro.KerningTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Character" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontFeatureTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontWeightPair" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.Timeline, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Timeline.AnimationPlayableAsset" preserve="all" />
    <type fullname="UnityEngine.Timeline.AnimationTrack" preserve="all" />
    <type fullname="UnityEngine.Timeline.MarkerTrack" preserve="all" />
    <type fullname="UnityEngine.Timeline.TimelineAsset" preserve="all" />
    <type fullname="UnityEngine.Timeline.MarkerList" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Timeline.TimelineAsset/EditorSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Timeline.TimelineClip" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.AnimationModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.AnimationClip" preserve="all" />
    <type fullname="UnityEngine.Animator" preserve="all" />
    <type fullname="UnityEngine.AnimatorOverrideController" preserve="all" />
    <type fullname="UnityEngine.Avatar" preserve="all" />
    <type fullname="UnityEngine.RuntimeAnimatorController" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.AudioModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.AudioListener" preserve="all" />
    <type fullname="UnityEngine.AudioSource" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Camera" preserve="all" />
    <type fullname="UnityEngine.FlareLayer" preserve="all" />
    <type fullname="UnityEngine.GameObject" preserve="all" />
    <type fullname="UnityEngine.Light" preserve="all" />
    <type fullname="UnityEngine.LightingSettings" preserve="all" />
    <type fullname="UnityEngine.LightmapSettings" preserve="all" />
    <type fullname="UnityEngine.LODGroup" preserve="all" />
    <type fullname="UnityEngine.Material" preserve="all" />
    <type fullname="UnityEngine.Mesh" preserve="all" />
    <type fullname="UnityEngine.MeshFilter" preserve="all" />
    <type fullname="UnityEngine.MeshRenderer" preserve="all" />
    <type fullname="UnityEngine.MonoBehaviour" preserve="all" />
    <type fullname="UnityEngine.Object" preserve="all" />
    <type fullname="UnityEngine.RectTransform" preserve="all" />
    <type fullname="UnityEngine.ReflectionProbe" preserve="all" />
    <type fullname="UnityEngine.RenderSettings" preserve="all" />
    <type fullname="UnityEngine.Shader" preserve="all" />
    <type fullname="UnityEngine.SkinnedMeshRenderer" preserve="all" />
    <type fullname="UnityEngine.Sprite" preserve="all" />
    <type fullname="UnityEngine.TextAsset" preserve="all" />
    <type fullname="UnityEngine.Texture2D" preserve="all" />
    <type fullname="UnityEngine.Transform" preserve="all" />
    <type fullname="UnityEngine.U2D.SpriteAtlas" preserve="all" />
    <type fullname="UnityEngine.Events.PersistentCallGroup" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.ArgumentCache" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.PersistentListenerMode" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.RectOffset" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.ExposedReference`1[Cinemachine.CinemachineVirtualCameraBase]" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.DirectorModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Playables.PlayableDirector" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.ParticleSystemModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.ParticleSystem" preserve="all" />
    <type fullname="UnityEngine.ParticleSystemRenderer" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.PhysicsModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.BoxCollider" preserve="all" />
    <type fullname="UnityEngine.CapsuleCollider" preserve="all" />
    <type fullname="UnityEngine.ConfigurableJoint" preserve="all" />
    <type fullname="UnityEngine.HingeJoint" preserve="all" />
    <type fullname="UnityEngine.MeshCollider" preserve="all" />
    <type fullname="UnityEngine.PhysicMaterial" preserve="all" />
    <type fullname="UnityEngine.Rigidbody" preserve="all" />
    <type fullname="UnityEngine.SphereCollider" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.TextRenderingModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Font" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.UI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.UI.CanvasScaler" preserve="all" />
    <type fullname="UnityEngine.UI.GraphicRaycaster" preserve="all" />
    <type fullname="UnityEngine.UI.HorizontalLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.UI.Image" preserve="all" />
    <type fullname="UnityEngine.UI.LayoutElement" preserve="all" />
    <type fullname="UnityEngine.UI.Text" preserve="all" />
    <type fullname="UnityEngine.UI.VerticalLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.UI.MaskableGraphic/CullStateChangedEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.FontData" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.UIModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Canvas" preserve="all" />
    <type fullname="UnityEngine.CanvasRenderer" preserve="all" />
  </assembly>
  <assembly fullname="DebugMenu.Runtime">
    <type fullname="RotaryHeart.Lib.SerializableDictionary.ReorderableList" preserve="nothing" serialized="true" />
    <type fullname="RotaryHeart.Lib.SerializableDictionary.SerializableDictionaryBase`2[GymkhanaVisualEffects.GymkhanaZoneState,GymkhanaVisualEffects.AnimationChangingEffect/AnimationKey]" preserve="nothing" serialized="true" />
    <type fullname="RotaryHeart.Lib.SerializableDictionary.SerializableDictionaryBase`2[GymkhanaVisualEffects.GymkhanaZoneState,System.Boolean]" preserve="nothing" serialized="true" />
    <type fullname="RotaryHeart.Lib.SerializableDictionary.SerializableDictionaryBase`2[GymkhanaVisualEffects.GymkhanaZoneState,UnityEngine.Color]" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.Mathematics">
    <type fullname="Unity.Mathematics.float2" preserve="nothing" serialized="true" />
    <type fullname="Unity.Mathematics.float3" preserve="nothing" serialized="true" />
    <type fullname="Unity.Mathematics.float4" preserve="nothing" serialized="true" />
    <type fullname="Unity.Mathematics.quaternion" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.IMGUIModule">
    <type fullname="UnityEngine.GUIContent" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.TextCoreFontEngineModule">
    <type fullname="UnityEngine.TextCore.FaceInfo" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.Glyph" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphMetrics" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphRect" preserve="nothing" serialized="true" />
  </assembly>
</linker>